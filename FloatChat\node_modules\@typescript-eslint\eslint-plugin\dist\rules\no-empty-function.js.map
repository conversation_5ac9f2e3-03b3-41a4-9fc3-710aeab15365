{"version": 3, "file": "no-empty-function.js", "sourceRoot": "", "sources": ["../../src/rules/no-empty-function.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAE1D,8CAAgC;AAChC,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,mBAAmB,CAAC,CAAC;AAKxD,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS;AAC3B,yHAAyH;AACzH,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;IACjC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EACxB;IACE,UAAU,EAAE;QACV,KAAK,EAAE;YACL,KAAK,EAAE;gBACL,IAAI,EAAE;oBACJ,WAAW;oBACX,gBAAgB;oBAChB,oBAAoB;oBACpB,SAAS;oBACT,kBAAkB;oBAClB,SAAS;oBACT,SAAS;oBACT,cAAc;oBACd,sBAAsB;oBACtB,wBAAwB;oBACxB,gBAAgB;oBAChB,cAAc;oBACd,oBAAoB;oBACpB,iBAAiB;iBAClB;aACF;SACF;KACF;CACF,CACF,CAAC;AAEF,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,mBAAmB;IACzB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,0BAA0B;YACvC,WAAW,EAAE,OAAO;YACpB,eAAe,EAAE,IAAI;SACtB;QACD,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc;QAC5C,MAAM,EAAE,CAAC,MAAM,CAAC;QAChB,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;KACjC;IACD,cAAc,EAAE;QACd;YACE,KAAK,EAAE,EAAE;SACV;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,KAAK,GAAG,EAAE,EAAE,CAAC;QAC9B,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEvC,MAAM,8BAA8B,GAAG,KAAK,CAAC,QAAQ,CACnD,wBAAwB,CACzB,CAAC;QACF,MAAM,4BAA4B,GAAG,KAAK,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;QAC5E,MAAM,2BAA2B,GAAG,KAAK,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;QACzE,MAAM,wBAAwB,GAAG,KAAK,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;QAEnE;;;;;WAKG;QACH,SAAS,WAAW,CAClB,IAAgE;YAEhE,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;QACnD,CAAC;QAED;;;;;WAKG;QACH,SAAS,sBAAsB,CAC7B,IAAgE;;YAEhE,OAAO,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,CACtB,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,CAC3D,CAAC;QACJ,CAAC;QAED;;;;WAIG;QACH,SAAS,yBAAyB,CAChC,IAAgE;YAEhE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAC3B,IACE,WAAW,CAAC,IAAI,CAAC;gBACjB,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,MAAK,sBAAc,CAAC,gBAAgB;gBAChD,MAAM,CAAC,IAAI,KAAK,aAAa,EAC7B;gBACA,MAAM,EAAE,aAAa,EAAE,GAAG,MAAM,CAAC;gBAEjC,OAAO;gBACL,+BAA+B;gBAC/B,CAAC,aAAa,KAAK,WAAW,IAAI,8BAA8B,CAAC;oBACjE,6BAA6B;oBAC7B,CAAC,aAAa,KAAK,SAAS,IAAI,4BAA4B,CAAC;oBAC7D,qDAAqD;oBACrD,sBAAsB,CAAC,IAAI,CAAC,CAC7B,CAAC;aACH;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED;;;;WAIG;QACH,SAAS,gCAAgC,CACvC,IAAgE;;YAEhE,IAAI,2BAA2B,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;gBACpD,MAAM,UAAU,GACd,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,gBAAgB;oBACnD,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU;oBACxB,CAAC,CAAC,SAAS,CAAC;gBAChB,OAAO,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC;aAC5C;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,4BAA4B,CACnC,IAAiC;;YAEjC,OAAO,CACL,wBAAwB;gBACxB,WAAW,CAAC,IAAI,CAAC;gBACjB,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,gBAAgB;gBACrD,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,IAAI,CAC9B,CAAC;QACJ,CAAC;QAED,uCACK,KAAK,KACR,kBAAkB,CAAC,IAAI;gBACrB,IACE,yBAAyB,CAAC,IAAI,CAAC;oBAC/B,gCAAgC,CAAC,IAAI,CAAC;oBACtC,4BAA4B,CAAC,IAAI,CAAC,EAClC;oBACA,OAAO;iBACR;gBAED,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YACjC,CAAC;YACD,mBAAmB,CAAC,IAAI;gBACtB,IAAI,gCAAgC,CAAC,IAAI,CAAC,EAAE;oBAC1C,OAAO;iBACR;gBAED,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAClC,CAAC,IACD;IACJ,CAAC;CACF,CAAC,CAAC"}