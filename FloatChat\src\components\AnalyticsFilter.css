.analytics-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #F9FAFB 0%, #F3F4F6 100%);
  padding: 24px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.analytics-card {
  background: white;
  border: 1px solid #E5E7EB;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.analytics-header h2 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 700;
  color: #111827;
}

.analytics-header p {
  margin: 0 0 16px 0;
  color: #6B7280;
}

.filters {
  display: flex;
  flex-wrap: wrap;
  gap: 12px 16px;
  align-items: flex-end;
  margin-bottom: 16px;
}

.categories {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.chip {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  border-radius: 999px;
  background: #F9FAFB;
  border: 1px solid #E5E7EB;
  cursor: pointer;
  user-select: none;
}

.chip input { display: none; }

.chip.checked {
  background: #EEF2FF;
  border-color: #C7D2FE;
}

.filter {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.filter label {
  font-size: 12px;
  color: #6B7280;
}

.filter input[type="date"] {
  border: 1px solid #E5E7EB;
  border-radius: 8px;
  padding: 8px 10px;
  font-size: 14px;
}

.toggle-row {
  display: inline-flex;
  gap: 8px;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 8px;
  border: 1px solid #E5E7EB;
  background: #FFFFFF;
  color: #111827;
  font-size: 14px;
  cursor: pointer;
  transition: background 120ms ease, box-shadow 120ms ease, border-color 120ms ease;
}

.btn:hover {
  background: #F9FAFB;
}

.btn:focus-visible {
  outline: none;
  box-shadow: 0 0 0 3px rgba(14,165,233,0.3);
}

.btn-primary {
  background: #0EA5E9;
  color: white;
  border-color: #0EA5E9;
}

.btn-primary:hover {
  background: #0284C7;
  border-color: #0284C7;
}

.btn-secondary {
  background: #FFFFFF;
  color: #111827;
}

.actions {
  display: inline-flex;
  gap: 8px;
  margin-left: auto;
}

.summary {
  display: flex;
  gap: 12px;
  margin-left: auto;
}

.summary-item {
  background: #F9FAFB;
  border: 1px solid #E5E7EB;
  border-radius: 10px;
  padding: 10px 12px;
}

.summary-label {
  font-size: 11px;
  color: #6B7280;
}

.summary-value {
  font-size: 16px;
  font-weight: 700;
  color: #111827;
}

.chart-wrapper {
  width: 100%;
}

.table-wrapper {
  margin-top: 16px;
  overflow: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #E5E7EB;
}

.data-table th,
.data-table td {
  padding: 10px 12px;
  border-bottom: 1px solid #F3F4F6;
  text-align: left;
  font-size: 14px;
}

.data-table thead th {
  background: #F9FAFB;
  color: #6B7280;
  font-weight: 600;
}

.data-table thead th {
  position: sticky;
  top: 0;
  z-index: 1;
}

.data-table tbody tr:hover {
  background: #FAFAFA;
}

.data-table .empty {
  text-align: center;
  color: #6B7280;
}

@media (max-width: 768px) {
  .analytics-page { padding: 16px; }
  .analytics-card { padding: 16px; }
  .summary { width: 100%; margin-left: 0; justify-content: space-between; }
}


