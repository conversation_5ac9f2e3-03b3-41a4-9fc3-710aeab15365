.app {
  min-height: 100vh;
  width: 100vw;
  background: linear-gradient(135deg, #F9FAFB 0%, #F3F4F6 100%);
  display: flex;
  flex-direction: column;
}

.app-content {
  flex: 1;
  /* margin-left: 280px; / / Remove this line */
  overflow: hidden;
}

@media (max-width: 768px) {
  .app-content {
    /* margin-left: 0; / / This is no longer needed */
    flex: 1;
  }
}
/* Add this to App.css or the relevant CSS file */
header, footer {
  width: 100%;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
/* Add to App.css */
body, html, #root {
  margin: 0;
  padding: 0;
  height: 100%;
}

.app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}