/* ChatGPT-Style Full-Screen Interface */

/* Main Container - Enhanced Layout */
.chatgpt-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  position: relative;
  transition: all 0.3s ease;
}

/* Light Theme */
.chatgpt-container.light-theme {
  background: #f7f7f8;
  color: #374151;
}

/* Dark Theme */
.chatgpt-container.dark-theme {
  background: #1a1a1a;
  color: #e5e5e5;
}

/* Sidebar Styles */
.sidebar {
  width: 260px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  border-right: 1px solid;
  overflow: hidden;
}

.light-theme .sidebar {
  background: #f9f9f9;
  border-right-color: #e5e5e5;
}

.dark-theme .sidebar {
  background: #171717;
  border-right-color: #404040;
}

.sidebar-closed {
  width: 0;
  min-width: 0;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.light-theme .sidebar-header {
  border-bottom-color: #e5e5e5;
}

.dark-theme .sidebar-header {
  border-bottom-color: #404040;
}

.new-chat-btn {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid;
  border-radius: 8px;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.light-theme .new-chat-btn {
  border-color: #d1d5db;
  color: #374151;
}

.light-theme .new-chat-btn:hover {
  background: #f3f4f6;
  border-color: #10a37f;
}

.dark-theme .new-chat-btn {
  border-color: #404040;
  color: #e5e5e5;
}

.dark-theme .new-chat-btn:hover {
  background: #2d2d2d;
  border-color: #10a37f;
}

/* Chat History */
.chat-history {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.chat-history-header {
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 600;
  opacity: 0.7;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.chat-history-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  border-radius: 8px;
  transition: all 0.2s ease;
  position: relative;
}

.chat-history-item:hover {
  background: rgba(0, 0, 0, 0.05);
}

.dark-theme .chat-history-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.chat-history-item.active {
  background: rgba(16, 163, 127, 0.1);
}

.chat-item-content {
  flex: 1;
  padding: 12px;
  cursor: pointer;
}

.chat-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.light-theme .chat-title {
  color: #374151;
}

.dark-theme .chat-title {
  color: #e5e5e5;
}

.chat-timestamp {
  font-size: 12px;
  opacity: 0.6;
}

.delete-chat-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  opacity: 0;
  transition: all 0.2s ease;
  margin-right: 8px;
}

.chat-history-item:hover .delete-chat-btn {
  opacity: 0.7;
}

.delete-chat-btn:hover {
  opacity: 1 !important;
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* Sidebar Footer */
.sidebar-footer {
  padding: 16px;
  border-top: 1px solid;
}

.light-theme .sidebar-footer {
  border-top-color: #e5e5e5;
}

.dark-theme .sidebar-footer {
  border-top-color: #404040;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 2px;
}

.light-theme .user-name {
  color: #374151;
}

.dark-theme .user-name {
  color: #e5e5e5;
}

.user-plan {
  font-size: 12px;
  opacity: 0.6;
}

/* Main Chat Area */
.main-chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100vh;
}

/* Chat Window - Full Screen */
.chatgpt-window {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.3s ease;
}

.light-theme .chatgpt-window {
  background: #f7f7f8;
}

.dark-theme .chatgpt-window {
  background: #1a1a1a;
}

/* Enhanced Header */
.chatgpt-header {
  padding: 16px 80px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: fixed;
  top: -80px;
  left: 0;
  right: 0;
  z-index: 1000;
  min-height: 60px;
  transition: all 0.3s ease;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  transform: translateY(0);
}

.chatgpt-header.header-visible {
  top: 0;
  transform: translateY(0);
}

.light-theme .chatgpt-header {
  background: white;
  border-bottom: 1px solid #e5e5e5;
}

.dark-theme .chatgpt-header {
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.sidebar-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.light-theme .sidebar-toggle {
  color: #718096;
}

.light-theme .sidebar-toggle:hover {
  background: #f1f5f9;
  color: #2d3748;
}

.dark-theme .sidebar-toggle {
  color: #a0a0a0;
}

.dark-theme .sidebar-toggle:hover {
  background: #404040;
  color: #e5e5e5;
}

.header-content h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: -0.025em;
}

.light-theme .header-content h3 {
  color: #2d3748;
}

.dark-theme .header-content h3 {
  color: #e5e5e5;
}

.chatgpt-header-subtitle {
  font-size: 14px;
  margin-top: 2px;
  font-weight: 400;
}

.model-selector {
  background: none;
  border: none;
  font-size: 14px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.light-theme .model-selector {
  color: #718096;
}

.light-theme .model-selector:hover {
  background: #f1f5f9;
  color: #2d3748;
}

.dark-theme .model-selector {
  color: #a0a0a0;
}

.dark-theme .model-selector:hover {
  background: #404040;
  color: #e5e5e5;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-btn,
.theme-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.light-theme .header-btn,
.light-theme .theme-toggle {
  color: #718096;
}

.light-theme .header-btn:hover,
.light-theme .theme-toggle:hover {
  background: #f1f5f9;
  color: #2d3748;
}

.dark-theme .header-btn,
.dark-theme .theme-toggle {
  color: #a0a0a0;
}

.dark-theme .header-btn:hover,
.dark-theme .theme-toggle:hover {
  background: #404040;
  color: #e5e5e5;
}

/* Floating Theme Toggle */
.floating-theme-toggle {
  position: fixed;
  bottom: 100px;
  right: 30px;
  z-index: 1000;
  background: none;
  border: none;
  cursor: pointer;
  padding: 12px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.light-theme .floating-theme-toggle {
  background: white;
  color: #374151;
  border: 1px solid #e5e5e5;
}

.light-theme .floating-theme-toggle:hover {
  background: #f9f9f9;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.dark-theme .floating-theme-toggle {
  background: #2d2d2d;
  color: #e5e5e5;
  border: 1px solid #404040;
}

.dark-theme .floating-theme-toggle:hover {
  background: #404040;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

/* Main Chat Area */
.main-chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  position: relative;
  align-items: center;
}

/* Messages Area - ChatGPT Style */
.chatgpt-messages {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  padding: 24px 80px;
  transition: all 0.3s ease;
}

.light-theme .chatgpt-messages {
  background: #f7f7f8;
}

.dark-theme .chatgpt-messages {
  background: #1a1a1a;
}

.chatgpt-messages::-webkit-scrollbar {
  width: 8px;
}

.chatgpt-messages::-webkit-scrollbar-track {
  background: transparent;
}

.light-theme .chatgpt-messages::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 4px;
}

.light-theme .chatgpt-messages::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

.dark-theme .chatgpt-messages::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 4px;
}

.dark-theme .chatgpt-messages::-webkit-scrollbar-thumb:hover {
  background: #525252;
}

/* Welcome Message */
.welcome-message {
  text-align: center;
  padding: 48px 24px;
  max-width: 768px;
  margin: 0 auto;
  transition: all 0.3s ease;
}

.light-theme .welcome-message {
  color: #6b7280;
}

.dark-theme .welcome-message {
  color: #9ca3af;
}

.welcome-message h2 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.light-theme .welcome-message h2 {
  color: #374151;
}

.dark-theme .welcome-message h2 {
  color: #e5e5e5;
}

.welcome-message p {
  font-size: 16px;
  line-height: 1.6;
}

/* Message Groups - Proper Alignment */
.message-group {
  margin-bottom: 24px;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.message-group.user {
  align-items: flex-end; /* User messages on the right */
}

.message-group.bot {
  align-items: flex-start; /* Bot messages on the left */
}

.message-content {
  display: flex;
  gap: 16px;
  align-items: flex-start;
  max-width: 100%;
}

.message-group.user .message-content {
  flex-direction: row-reverse; /* Avatar on right for user */
}

.message-group.bot .message-content {
  flex-direction: row; /* Avatar on left for bot */
}

.message-avatar {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}

.message-avatar.user {
  border-radius: 50%; /* Circular for user */
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.message-avatar.bot {
  border-radius: 50%; /* Circular for bot */
  background: #10a37f;
  color: white;
}

.message-bubble {
  max-width: 80%;
  padding: 12px 16px;
  border-radius: 18px;
  font-size: 15px;
  line-height: 1.5;
  word-wrap: break-word;
  position: relative;
  animation: messageSlideIn 0.3s ease-out;
  transition: all 0.2s ease;
}

.message-bubble:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.message-bubble.user {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-top-left-radius: 18px;
  border-top-right-radius: 18px;
  border-bottom-left-radius: 18px;
  border-bottom-right-radius: 4px;
}

.light-theme .message-bubble.bot {
  background: white;
  color: #374151;
  border: 1px solid #e5e7eb;
  border-top-left-radius: 18px;
  border-top-right-radius: 18px;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 18px;
}

.dark-theme .message-bubble.bot {
  background: #2d2d2d;
  color: #e5e5e5;
  border: 1px solid #404040;
  border-top-left-radius: 18px;
  border-top-right-radius: 18px;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 18px;
}

/* Message Wrapper and Actions */
.message-wrapper {
  display: flex;
  flex-direction: column;
  gap: 4px;
  max-width: 80%;
}

.message-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.message-group:hover .message-actions {
  opacity: 1;
}

.message-action-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.light-theme .message-action-btn {
  color: #9ca3af;
}

.light-theme .message-action-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.dark-theme .message-action-btn {
  color: #6b7280;
}

.dark-theme .message-action-btn:hover {
  background: #374151;
  color: #e5e5e5;
}

/* Quick Suggestions */
.quick-suggestions {
  margin-top: 32px;
  max-width: 600px;
}

.suggestions-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 16px;
  opacity: 0.8;
}

.suggestions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.suggestion-btn {
  padding: 12px 16px;
  border: 1px solid;
  border-radius: 12px;
  background: transparent;
  cursor: pointer;
  text-align: left;
  font-size: 14px;
  transition: all 0.2s ease;
}

.light-theme .suggestion-btn {
  border-color: #e5e7eb;
  color: #374151;
}

.light-theme .suggestion-btn:hover {
  background: #f9fafb;
  border-color: #10a37f;
  transform: translateY(-1px);
}

.dark-theme .suggestion-btn {
  border-color: #404040;
  color: #e5e5e5;
}

.dark-theme .suggestion-btn:hover {
  background: #2d2d2d;
  border-color: #10a37f;
  transform: translateY(-1px);
}

/* Typing Indicator - ChatGPT Style */
.typing-indicator {
  margin-bottom: 24px;
  width: 100%;
}

.typing-content {
  max-width: 768px;
  margin: 0 auto;
  padding: 24px;
  display: flex;
  gap: 16px;
  align-items: center;
}

.light-theme .typing-indicator {
  background: white;
  border-top: 1px solid #e5e5e5;
  border-bottom: 1px solid #e5e5e5;
}

.dark-theme .typing-indicator {
  background: #2d2d2d;
  border-top: 1px solid #404040;
  border-bottom: 1px solid #404040;
}

.typing-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%; /* Circular for bot typing indicator */
  background: #10a37f;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}

.typing-bubble {
  background: transparent;
  padding: 12px 16px;
  border-radius: 18px;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dot {
  width: 6px;
  height: 6px;
  background: #9ca3af;
  border-radius: 50%;
  animation: typing-bounce 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

/* Input Area - ChatGPT Style */
.chatgpt-input {
  padding: 24px 80px;
  position: sticky;
  bottom: 0;
  transition: all 0.3s ease;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.light-theme .chatgpt-input {
  background: white;
  border-top: 1px solid #e5e5e5;
}

.dark-theme .chatgpt-input {
  background: #2d2d2d;
  border-top: 1px solid #404040;
}

.chatgpt-input-container {
  max-width: 768px;
  margin: 0 auto;
  position: relative;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  position: relative;
  background: transparent;
  border-radius: 12px;
  padding: 4px;
  transition: all 0.2s ease;
}

.light-theme .input-wrapper {
  background: #f7f7f8;
}

.light-theme .input-wrapper:focus-within {
  background: #ffffff;
  box-shadow: 0 0 0 2px rgba(16, 163, 127, 0.1);
}

.dark-theme .input-wrapper {
  background: #1a1a1a;
}

.dark-theme .input-wrapper:focus-within {
  background: #2d2d2d;
  box-shadow: 0 0 0 2px rgba(16, 163, 127, 0.2);
}

.attachment-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 12px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  margin-bottom: 4px;
}

.light-theme .attachment-btn {
  color: #9ca3af;
}

.light-theme .attachment-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.dark-theme .attachment-btn {
  color: #6b7280;
}

.dark-theme .attachment-btn:hover {
  background: #374151;
  color: #e5e5e5;
}

.chatgpt-input-field {
  flex: 1;
  border-radius: 8px;
  padding: 12px 60px 12px 12px;
  font-size: 16px;
  outline: none;
  border: none;
  background: transparent;
  transition: all 0.2s ease;
  resize: none;
  min-height: 24px;
  max-height: 200px;
  font-family: inherit;
  box-shadow: none;
}

.light-theme .chatgpt-input-field {
  color: #374151;
}

.light-theme .chatgpt-input-field::placeholder {
  color: #9ca3af;
}

.dark-theme .chatgpt-input-field {
  color: #e5e5e5;
}

.dark-theme .chatgpt-input-field::placeholder {
  color: #6b7280;
}

.chatgpt-send-button {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #10a37f;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.chatgpt-send-button:hover:not(:disabled) {
  background: #0d8f6b;
}

.chatgpt-send-button:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.send-icon {
  width: 20px;
  height: 20px;
  fill: white;
}

/* Input Footer */
.input-footer {
  margin-top: 12px;
  text-align: center;
}

.input-info {
  font-size: 12px;
  opacity: 0.6;
  line-height: 1.4;
}

/* Animations */
@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typing-bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .chatgpt-messages {
    padding: 16px 32px;
    max-width: 100%;
  }

  .chatgpt-input {
    padding: 16px 32px;
  }

  .chatgpt-header {
    padding: 12px 32px;
  }

  .chatgpt-input-field {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 14px 50px 14px 14px;
  }

  .chatgpt-send-button {
    width: 36px;
    height: 36px;
    right: 6px;
  }

  .send-icon {
    width: 18px;
    height: 18px;
  }

  .chatgpt-header {
    padding: 12px 24px;
    min-height: 56px;
  }

  .header-content h3 {
    font-size: 15px;
  }

  .chatgpt-header-subtitle {
    font-size: 13px;
  }

  .welcome-message {
    padding: 32px 16px;
  }

  .welcome-message h2 {
    font-size: 20px;
  }

  .welcome-message p {
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .message-group {
    margin-bottom: 16px;
  }

  .message-content {
    gap: 12px;
  }

  .message-avatar {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .message-bubble {
    max-width: 85%;
    font-size: 14px;
    padding: 10px 14px;
  }

  .typing-avatar {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .typing-dot {
    width: 5px;
    height: 5px;
  }

  .chatgpt-input-field {
    padding: 12px 45px 12px 12px;
    font-size: 16px;
  }

  .chatgpt-send-button {
    width: 32px;
    height: 32px;
    right: 6px;
  }

  .send-icon {
    width: 16px;
    height: 16px;
  }

  .theme-toggle {
    padding: 6px;
  }
}

@media (max-width: 320px) {
  .chatgpt-messages {
    padding: 12px 20px;
  }

  .chatgpt-input {
    padding: 12px 20px;
  }

  .chatgpt-header {
    padding: 12px 20px;
  }

  .message-bubble {
    max-width: 90%;
    font-size: 13px;
    padding: 8px 12px;
  }

  .welcome-message {
    padding: 24px 12px;
  }

  .welcome-message h2 {
    font-size: 18px;
  }

  .welcome-message p {
    font-size: 14px;
  }
}





