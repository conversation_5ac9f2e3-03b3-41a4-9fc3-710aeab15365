/* Header.css */
.header-nav {
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 0.75rem 1rem;
  position: relative;
  z-index: 50;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.logo-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: transform 0.2s;
  text-decoration: none;
  flex-shrink: 0;
}

.logo-container:hover {
  transform: scale(1.02);
}

.logo-img {
  width: 2.5rem;
  height: 2.5rem;
  background: linear-gradient(135deg, #6b21a8 0%, #db2777 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.logo-img i {
  color: white;
  font-size: 1.5rem;
}

.logo-text {
  color: #1f2937;
  font-size: 1.75rem;
  font-weight: 800;
  letter-spacing: 0.5px;
}

/* Centered Navigation */
.centered-nav {
  display: flex;
  justify-content: center;
  flex: 1;
  margin: 0 2rem;
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 1.5rem;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  color: #4b5563;
  font-weight: 500;
  transition: all 0.2s;
  text-decoration: none;
  font-size: 1rem;
}

.nav-link:hover {
  background: #f3f4f6;
  color: #1f2937;
}

.nav-link.active {
  background: #f3f4f6;
  color: #1f2937;
}

.nav-link i {
  font-size: 1.1rem;
}

/* Right Section */
.right-section {
  display: flex;
  align-items: center;
  position: relative;
  flex-shrink: 0;
}

.auth-buttons {
  display: flex;
  gap: 0.75rem;
}

.auth-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-weight: 600;
  transition: all 0.2s;
  border: none;
  cursor: pointer;
  font-family: inherit;
  font-size: 0.95rem;
}

/* Blue login button */
.login-btn {
  background: #2563eb;
  color: white;
  border: 1px solid #2563eb;
}

.login-btn:hover {
  background: #1d4ed8;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(37, 99, 235, 0.2);
}

/* Settings button */
.settings-btn {
  background: #f3f4f6;
  color: #4b5563;
  border: 1px solid #e5e7eb;
}

.settings-btn:hover {
  background: #e5e7eb;
  transform: translateY(-2px);
}

/* Logout button style */
.logout-btn {
  background: #2563eb;
  color: rgb(235, 233, 233);
  border: 1px solid #2563eb;
}

.logout-btn:hover {
  background: #b91c1c;
  border:1px solid #b91c1c;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(228, 224, 224, 0.2);
}

.user-btn {
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  margin-left: 0.75rem;
}

.user-btn:hover {
  background: #e5e7eb;
  transform: scale(1.05);
}

.user-avatar {
  color: #4b5563;
  font-size: 1.25rem;
}

.dropdown-menu {
  position: absolute;
  right: 0;
  top: 3.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  width: 200px;
  overflow: hidden;
  z-index: 100;
  animation: dropdownFade 0.2s ease-out;
  border: 1px solid #e5e7eb;
}

@keyframes dropdownFade {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-list {
  list-style: none;
  padding: 0.5rem 0;
  margin: 0;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem 1rem;
  background: none;
  border: none;
  cursor: pointer;
  color: #374151;
  font-size: 0.95rem;
  transition: all 0.2s;
  font-family: inherit;
}

.dropdown-item:hover {
  background: #f3f4f6;
  color: #6b21a8;
}

.dropdown-item i {
  width: 1.25rem;
  text-align: center;
}

.mobile-menu-btn {
  display: none;
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  width: 2.5rem;
  height: 2.5rem;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #4b5563;
  transition: all 0.2s;
  font-family: inherit;
  margin-left: 0.75rem;
}

.mobile-menu-btn:hover {
  background: #e5e7eb;
}

/* Mobile Navigation */
.mobile-nav {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  z-index: 40;
  border-top: 1px solid #e5e7eb;
}

.mobile-nav-open {
  display: block;
  animation: menuSlide 0.3s ease-out;
}

@keyframes menuSlide {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mobile-nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  background: none;
  border: none;
  cursor: pointer;
  color: #4b5563;
  font-weight: 500;
  transition: all 0.2s;
  width: 100%;
  text-align: left;
  font-family: inherit;
  font-size: 1rem;
  text-decoration: none;
}

.mobile-nav-link:hover {
  background: #f3f4f6;
  color: #1f2937;
}

.mobile-nav-link.logout-btn {
  color: #dc2626;
}

.mobile-nav-link.logout-btn:hover {
  background: #fef2f2;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Media Queries */
@media (max-width: 968px) {
  .centered-nav {
    display: none;
  }
  
  .mobile-menu-btn {
    display: flex;
  }
  
  .auth-buttons {
    display: none;
  }
}

@media (min-width: 969px) {
  .mobile-nav {
    display: none !important;
  }
  
  .centered-nav {
    display: flex;
  }
  
  .auth-buttons {
    display: flex;
  }
}

@media (max-width: 640px) {
  .logo-text {
    font-size: 1.5rem;
  }
  
  .nav-container {
    padding: 0 0.5rem;
  }
}
.logo-img {
  width: 5.5rem;
  height: 5.5rem;
  /* background: linear-gradient(135deg, #6b21a8 0%, #db2777 100%); */ /* Remove this line */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}