{"version": 3, "file": "no-loop-func.js", "sourceRoot": "", "sources": ["../../src/rules/no-loop-func.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAE1D,8CAAgC;AAChC,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,cAAc,CAAC,CAAC;AAKnD,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,cAAc;IACpB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,sFAAsF;YACxF,WAAW,EAAE,KAAK;YAClB,eAAe,EAAE,IAAI;SACtB;QACD,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc;QAC5C,MAAM,EAAE,EAAE;QACV,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;KACjC;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ;;;;;;;WAOG;QACH,SAAS,aAAa,CACpB,IAGgC;YAEhC,MAAM,QAAQ,GAAG,qBAAqB,CAAC,IAAI,CAAC,CAAC;YAE7C,IAAI,CAAC,QAAQ,EAAE;gBACb,OAAO;aACR;YAED,MAAM,UAAU,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC;YAC9C,MAAM,UAAU,GAAG,UAAU;iBAC1B,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;iBACjC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAE/B,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;gBACzB,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,YAAY;oBACvB,IAAI,EAAE,EAAE,QAAQ,EAAE,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;iBACnD,CAAC,CAAC;aACJ;QACH,CAAC;QAED,OAAO;YACL,uBAAuB,EAAE,aAAa;YACtC,kBAAkB,EAAE,aAAa;YACjC,mBAAmB,EAAE,aAAa;SACnC,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH;;;;;;;;GAQG;AACH,SAAS,qBAAqB,CAAC,IAAmB;IAChD,KACE,IAAI,WAAW,GAAG,IAAI,EACtB,WAAW,CAAC,MAAM,EAClB,WAAW,GAAG,WAAW,CAAC,MAAM,EAChC;QACA,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;QAElC,QAAQ,MAAM,CAAC,IAAI,EAAE;YACnB,KAAK,sBAAc,CAAC,cAAc,CAAC;YACnC,KAAK,sBAAc,CAAC,gBAAgB;gBAClC,OAAO,MAAM,CAAC;YAEhB,KAAK,sBAAc,CAAC,YAAY;gBAC9B,iCAAiC;gBACjC,IAAI,MAAM,CAAC,IAAI,KAAK,WAAW,EAAE;oBAC/B,OAAO,MAAM,CAAC;iBACf;gBACD,MAAM;YAER,KAAK,sBAAc,CAAC,cAAc,CAAC;YACnC,KAAK,sBAAc,CAAC,cAAc;gBAChC,kCAAkC;gBAClC,IAAI,MAAM,CAAC,KAAK,KAAK,WAAW,EAAE;oBAChC,OAAO,MAAM,CAAC;iBACf;gBACD,MAAM;YAER,KAAK,sBAAc,CAAC,uBAAuB,CAAC;YAC5C,KAAK,sBAAc,CAAC,kBAAkB,CAAC;YACvC,KAAK,sBAAc,CAAC,mBAAmB;gBACrC,2CAA2C;gBAC3C,OAAO,IAAI,CAAC;YAEd;gBACE,MAAM;SACT;KACF;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;GAMG;AACH,SAAS,cAAc,CACrB,IAAmB,EACnB,YAA8C;IAE9C,MAAM,MAAM,GAAG,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxD,IAAI,IAAI,GAAG,IAAI,CAAC;IAChB,IAAI,kBAAkB,GAAyB,IAAI,CAAC;IAEpD,OAAO,kBAAkB,IAAI,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE;QAClE,IAAI,GAAG,kBAAkB,CAAC;QAC1B,kBAAkB,GAAG,qBAAqB,CAAC,kBAAkB,CAAC,CAAC;KAChE;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;GAMG;AACH,SAAS,MAAM,CACb,QAAuB,EACvB,SAAmC;;IAEnC,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;IACpC,MAAM,UAAU,GAAG,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IACrC,MAAM,WAAW,GAAG,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,MAAM,CAAC;IACvC,MAAM,IAAI,GACR,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,IAAI,MAAK,sBAAc,CAAC,mBAAmB;QACtD,CAAC,CAAC,WAAW,CAAC,IAAI;QAClB,CAAC,CAAC,EAAE,CAAC;IAET,+BAA+B;IAC/B,yEAAyE;IACzE,IAAI,SAAS,CAAC,eAAe,EAAE;QAC7B,OAAO,IAAI,CAAC;KACb;IAED,mDAAmD;IACnD,IAAI,IAAI,KAAK,OAAO,EAAE;QACpB,OAAO,IAAI,CAAC;KACb;IAED;;;OAGG;IACH,IACE,IAAI,KAAK,KAAK;QACd,WAAW;QACX,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;QACxC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EACxC;QACA,OAAO,IAAI,CAAC;KACb;IAED;;;OAGG;IACH,MAAM,MAAM,GAAG,cAAc,CAAC,QAAQ,EAAE,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC;SACzE,KAAK,CAAC,CAAC,CAAC,CAAC;IAEZ;;;;;;;;;;;OAWG;IACH,SAAS,eAAe,CAAC,QAAkC;;QACzD,MAAM,EAAE,GAAG,QAAQ,CAAC,UAAU,CAAC;QAE/B,OAAO,CACL,CAAC,QAAQ,CAAC,OAAO,EAAE;YACnB,CAAC,CAAA,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,KAAK,0CAAE,aAAa,MAAK,QAAQ,CAAC,IAAI,CAAC,aAAa;gBAC7D,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CACxB,CAAC;IACJ,CAAC;IAED,OAAO,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,UAAU,CAAC,KAAK,CAAC,eAAe,CAAC,mCAAI,KAAK,CAAC;AAC9D,CAAC"}