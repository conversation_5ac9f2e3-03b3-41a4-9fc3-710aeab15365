/* OceanExplorerAuth.css */
.ocean-auth-container {
  min-height: 100vh;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #0077be 0%, #005a87 100%);
  padding: 20px;
  margin: 0;
  overflow: auto;
  position: relative;
  box-sizing: border-box;
}

.ocean-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  overflow: hidden;
}

.bubble {
  position: absolute;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  animation: float 15s infinite ease-in-out;
}

.bubble-1 {
  width: 20px;
  height: 20px;
  top: 80%;
  left: 10%;
  animation-delay: 0s;
}

.bubble-2 {
  width: 30px;
  height: 30px;
  top: 75%;
  left: 20%;
  animation-delay: 2s;
}

.bubble-3 {
  width: 15px;
  height: 15px;
  top: 70%;
  left: 30%;
  animation-delay: 4s;
}

.bubble-4 {
  width: 25px;
  height: 25px;
  top: 85%;
  left: 40%;
  animation-delay: 6s;
}

.bubble-5 {
  width: 18px;
  height: 18px;
  top: 75%;
  left: 50%;
  animation-delay: 8s;
}

@keyframes float {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) rotate(720deg);
    opacity: 0;
  }
}

.auth-content-container {
  width: 100%;
  max-width: 1000px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  z-index: 2;
  position: relative;
  margin: 20px 0;
}

.auth-content {
  display: flex;
  width: 100%;
  min-height: 600px;
}

.left-panel {
  flex: 1;
  background: linear-gradient(135deg, #1a365d 0%, #2a4365 100%);
  color: white;
  padding: 40px 30px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 25px;
}

.logo-text {
  font-size: 2rem;
  font-weight: 700;
}

.tagline {
  font-size: 1.6rem;
  font-weight: 600;
  margin-bottom: 25px;
  line-height: 1.3;
}

.left-panel .divider {
  height: 2px;
  background: rgba(255, 255, 255, 0.3);
  width: 60px;
  margin: 25px 0;
}

.features {
  margin-bottom: 30px;
}

.features-title {
  font-size: 1.2rem;
  margin-bottom: 20px;
  font-weight: 600;
}

.features-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
  font-size: 0.95rem;
}

.quote {
  margin-top: auto;
  font-style: italic;
  opacity: 0.9;
  border-left: 3px solid #ffd700;
  padding-left: 15px;
}

.quote-text {
  font-size: 0.9rem;
  margin: 0;
}

.right-panel {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px;
  background: #ffffff;
  overflow-y: auto;
}

.auth-card {
  width: 100%;
  max-width: 400px;
}

.auth-header {
  text-align: center;
  margin-bottom: 25px;
}

.auth-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1a365d;
  margin-bottom: 8px;
}

.auth-subtitle {
  color: #718096;
  margin-bottom: 0;
  font-size: 0.95rem;
}

.social-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 25px;
}

.social-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 12px 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.social-btn:hover {
  background: #f7fafc;
}

.google-btn:hover {
  border-color: #4299e1;
}

.facebook-btn:hover {
  border-color: #3182ce;
}

.social-icon {
  width: 18px;
  height: 18px;
}

.right-panel .divider {
  display: flex;
  align-items: center;
  margin: 20px 0;
  color: #718096;
}

.divider::before,
.divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background: #e2e8f0;
}

.divider-text {
  padding: 0 12px;
  font-size: 0.85rem;
}

.auth-form {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #2d3748;
  font-size: 0.9rem;
}

.input-wrapper {
  position: relative;
}

.input-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #a0aec0;
}

.form-input {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.95rem;
  transition: border-color 0.2s;
  background: #f7fafc;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
  background: white;
}

.form-input.error {
  border-color: #e53e3e;
}

.password-toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #a0aec0;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-text {
  display: block;
  margin-top: 6px;
  font-size: 0.8rem;
  color: #e53e3e;
}

.submit-error {
  text-align: center;
  margin-top: 12px;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  color: #4a5568;
  font-size: 0.9rem;
  white-space: nowrap;
}

.checkbox-label input {
  margin: 0;
  accent-color: #4299e1;
}

.forgot-password {
  background: none;
  border: none;
  color: #4299e1;
  font-size: 0.85rem;
  cursor: pointer;
  font-weight: 500;
  white-space: nowrap;
}

.forgot-password:hover {
  color: #3182ce;
  text-decoration: underline;
}

.auth-button {
  width: 100%;
  padding: 14px;
  background: linear-gradient(135deg, #0077be 0%, #005a87 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  box-shadow: 0 4px 6px rgba(0, 119, 190, 0.2);
}

.auth-button:hover {
  background: linear-gradient(135deg, #005a87 0%, #00476b 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(0, 119, 190, 0.3);
}

.auth-button:disabled {
  background: #a0aec0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.auth-footer {
  text-align: center;
  margin-top: 20px;
}

.footer-text {
  margin-bottom: 15px;
  color: #718096;
  font-size: 0.9rem;
}

.auth-link {
  background: none;
  border: none;
  color: #4299e1;
  cursor: pointer;
  font-weight: 600;
  margin-left: 5px;
  font-size: 0.9rem;
}

.auth-link:hover {
  text-decoration: underline;
}

.terms {
  font-size: 0.75rem;
  margin-top: 15px;
  line-height: 1.5;
  color: #718096;
  padding: 0 10px;
}

.link {
  color: #4299e1;
  text-decoration: none;
  margin: 0 3px;
  font-weight: 500;
}

.link:hover {
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 900px) {
  .auth-content {
    flex-direction: column;
    min-height: auto;
  }
  
  .left-panel {
    padding: 30px 20px;
    order: 2;
  }
  
  .right-panel {
    padding: 30px 20px;
    order: 1;
  }
  
  .logo {
    justify-content: center;
    margin-bottom: 20px;
  }
  
  .logo-text {
    font-size: 1.8rem;
  }
  
  .tagline {
    font-size: 1.4rem;
    text-align: center;
  }
  
  .features {
    text-align: center;
  }
  
  .feature-item {
    justify-content: center;
  }
  
  .quote {
    text-align: center;
    border-left: none;
    padding-left: 0;
    border-top: 2px solid rgba(255, 215, 0, 0.3);
    padding-top: 15px;
  }
  
  .left-panel .divider {
    margin: 20px auto;
  }
}

@media (max-width: 600px) {
  .ocean-auth-container {
    padding: 10px;
  }
  
  .auth-content-container {
    margin: 10px 0;
    border-radius: 12px;
  }
  
  .left-panel, .right-panel {
    padding: 25px 15px;
  }
  
  .logo-text {
    font-size: 1.6rem;
  }
  
  .tagline {
    font-size: 1.2rem;
  }
  
  .auth-title {
    font-size: 1.6rem;
  }
  
  .form-options {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .forgot-password {
    align-self: flex-end;
  }
  
  .social-buttons {
    gap: 10px;
  }
  
  .social-btn {
    padding: 10px 14px;
    font-size: 0.85rem;
  }
  
  .auth-button {
    padding: 12px;
    font-size: 0.95rem;
  }
}

@media (max-width: 400px) {
  .logo {
    flex-direction: column;
    gap: 8px;
  }
  
  .feature-item {
    flex-direction: column;
    text-align: center;
    gap: 5px;
  }
  
  .social-btn {
    font-size: 0.8rem;
  }
  
  .divider-text {
    font-size: 0.8rem;
  }
}